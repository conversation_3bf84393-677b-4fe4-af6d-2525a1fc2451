'use client';

import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Globe, Calendar, Code, Laptop, ShoppingCart, Database, Server, Filter, Search, ExternalLink, Github, ChevronDown, X, Check, Tag, Clock } from 'lucide-react';
import { MotionWrapper } from '../animations/MotionWrapper';
import { TiltCard } from '../animations/AnimatedCard';
import Image from 'next/image';

interface ProjectItem {
  id: number;
  name: string;
  period: string;
  description: string;
  category: string;
  technologies: string[];
  github?: string;
  demo?: string;
  image?: string;
  featured?: boolean;
}

const projects: ProjectItem[] = [
  {
    id: 1,
    name: 'Online Payment Gateway',
    period: 'January 2024 - April 2024',
    description: 'Led a team of 5 to implement and integrate an online payment gateway, enhancing the customer experience and streamlining the online payment process. The implementation resulted in a 30% increase in completed orders and expanded payment options for customers.',
    category: 'Web Development',
    technologies: ['API Integration', 'Payment Processing', 'Shopify'],

    featured: true
  },
  {
    id: 2,
    name: 'ERP Integration',
    period: 'March 2024 - June 2024',
    description: 'Spearheaded the addition of ERP features, including accounting, payment, and operations functionalities, ensuring seamless integration with third-party ERP systems. This initiative saved the company over $100K annually in software licensing costs.',
    category: 'Enterprise Systems',
    technologies: ['ERP', 'System Integration', 'Business Process Automation'],

    featured: true
  },
  {
    id: 3,
    name: 'Zawaya Backend System',
    period: 'January 2023 - Present',
    description: 'Developed the backend system for Zawaya from scratch to comply with specific business needs, enhancing operational efficiency and system reliability. The system now handles over 10,000 transactions daily with 99.9% uptime.',
    category: 'Backend Development',
    technologies: ['API Development', 'Database Design', 'System Architecture'],

  },
  {
    id: 4,
    name: 'Internal Flutter Delivery App',
    period: 'November 2023 - Present',
    description: 'Designed, coded, and tested a Flutter-based delivery app that provides real-time tracking of agents\' performance and availability, enhancing operational efficiency. The app reduced delivery times by 28% and increased deliveries per agent by 35%.',
    category: 'Mobile Development',
    technologies: ['Flutter', 'Dart', 'Real-time Tracking', 'Firebase'],

    featured: true
  },
  {
    id: 5,
    name: 'Shopify Website Development',
    period: 'January 2023 - Present',
    description: 'Developed a new Shopify e-commerce website, improving user experience and driving an increase in customer orders. The new site increased conversion rates by 45% and reduced cart abandonment by 30%.',
    category: 'E-commerce',
    technologies: ['Shopify', 'E-commerce', 'UI/UX Design', 'JavaScript'],
    image: '/assets/images/zawaya-ecommerce.jpg'
  },
  {
    id: 6,
    name: 'Internal Inventory Management Web App',
    period: 'March 2023 - Present',
    description: 'Designed, coded, and tested a TypeScript/React/Node web app for the warehouse team that helps track item movement, sales orders, and purchase orders, keeping the team performance always in check. The system reduced inventory discrepancies by 75%.',
    category: 'Web Development',
    technologies: ['React', 'TypeScript', 'Node.js', 'Inventory Management'],
    image: '/assets/images/crm-webapp.jpg'
  },
  {
    id: 7,
    name: 'Internal CRM Web App',
    period: 'March 2023 - Present',
    description: 'Designed, coded, and tested a TypeScript/React/Node web app for the sales team that tracks RFQs, QTNs, won and lost orders over the team, and keeps track of every action and its reactions to help understand customer and team behavior, leading to tracking performance and finance insights.',
    category: 'Web Development',
    technologies: ['React', 'TypeScript', 'Node.js', 'CRM'],
    image: '/assets/images/crm-webapp.jpg'
  }
];

const categories = ['All', 'Web Development', 'Mobile Development', 'E-commerce', 'Enterprise Systems', 'Backend Development'];

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'Web Development':
      return <Globe className="h-5 w-5" />;
    case 'Mobile Development':
      return <Laptop className="h-5 w-5" />;
    case 'E-commerce':
      return <ShoppingCart className="h-5 w-5" />;
    case 'Enterprise Systems':
      return <Database className="h-5 w-5" />;
    case 'Backend Development':
      return <Server className="h-5 w-5" />;
    default:
      return <Code className="h-5 w-5" />;
  }
};

export default function ProjectsGrid() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProject, setSelectedProject] = useState<ProjectItem | null>(null);
  const [filtersOpen, setFiltersOpen] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  
  // Filter projects by category and search query
  const filteredProjects = projects.filter(project => {
    const matchesCategory = selectedCategory === 'All' || project.category === selectedCategory;
    const matchesSearch = 
      searchQuery === '' || 
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.technologies.some(tech => tech.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });
  
  // Sort projects to show featured ones first
  const sortedProjects = [...filteredProjects].sort((a, b) => {
    if (a.featured && !b.featured) return -1;
    if (!a.featured && b.featured) return 1;
    return 0;
  });
  
  const closeModal = (e: React.MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      setSelectedProject(null);
    }
  };
  
  return (
    <MotionWrapper className="space-y-8">
      {/* Controls and Filters */}
      <div className="flex flex-col md:flex-row gap-4 justify-between">
        <div className="relative max-w-md w-full">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </div>
          <input
            type="text"
            className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 p-2.5"
            placeholder="Search projects..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setFiltersOpen(!filtersOpen)}
            className="md:hidden bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2.5 flex items-center gap-2 text-gray-700 dark:text-gray-300"
          >
            <Filter className="w-5 h-5" />
            <span>Filters</span>
            <ChevronDown className={`w-4 h-4 transition-transform ${filtersOpen ? 'rotate-180' : ''}`} />
          </button>
          
          <div className={`md:flex flex-wrap gap-2 ${filtersOpen ? 'flex' : 'hidden'}`}>
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium transition-all ${
                  selectedCategory === category
                    ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200 ring-2 ring-indigo-300 dark:ring-indigo-700'
                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                {category !== 'All' && getCategoryIcon(category)}
                {category}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Project Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {sortedProjects.map((project, index) => (
            <TiltCard 
              key={project.id} 
              delay={index * 0.1}
              onClick={() => setSelectedProject(project)}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden h-full cursor-pointer group relative"
            >
              {project.featured && (
                <div className="absolute top-3 left-3 z-10">
                  <span className="bg-indigo-600 text-white text-xs font-semibold px-2.5 py-1 rounded-full flex items-center gap-1">
                    <Star className="w-3 h-3 fill-current" /> Featured
                  </span>
                </div>
              )}
              
              <div className="h-48 relative overflow-hidden">
                {project.image ? (
                  <img 
                    src={project.image} 
                    alt={project.name}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-indigo-400 to-purple-500 flex items-center justify-center">
                    {getCategoryIcon(project.category)}
                  </div>
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
                  <div className="absolute bottom-3 left-3">
                    <div className="flex items-center gap-1.5 text-white">
                      {getCategoryIcon(project.category)}
                      <span className="text-sm font-medium">{project.category}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="p-5">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 line-clamp-2">{project.name}</h3>
                <div className="flex items-center text-gray-500 dark:text-gray-400 text-sm mb-3">
                  <Calendar className="w-4 h-4 mr-1.5" />
                  <span>{project.period}</span>
                </div>
                <p className="text-gray-600 dark:text-gray-300 line-clamp-3 mb-4">{project.description}</p>
                
                <div className="flex flex-wrap gap-1.5 mt-auto">
                  {project.technologies.slice(0, 3).map((tech, i) => (
                    <span
                      key={i}
                      className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                    >
                      {tech}
                    </span>
                  ))}
                  {project.technologies.length > 3 && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                      +{project.technologies.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            </TiltCard>
          ))}
        </AnimatePresence>
      </div>
      
      {/* No Results Message */}
      {filteredProjects.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12 bg-white dark:bg-gray-800 rounded-xl shadow-md"
        >
          <div className="inline-block p-3 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
            <Search className="h-6 w-6 text-gray-500 dark:text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">No projects found</h3>
          <p className="text-gray-500 dark:text-gray-400 mt-2">Try adjusting your search or filter criteria</p>
          <button
            onClick={() => {
              setSelectedCategory('All');
              setSearchQuery('');
            }}
            className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
          >
            Clear Filters
          </button>
        </motion.div>
      )}
      
      {/* Project Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
            onClick={closeModal}
          >
            <motion.div
              ref={modalRef}
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 20, scale: 0.95 }}
              transition={{ type: 'spring', damping: 25 }}
              className="bg-white dark:bg-gray-800 w-full max-w-4xl max-h-[90vh] overflow-y-auto rounded-xl shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="relative">
                <div className="h-64 sm:h-80 overflow-hidden bg-gradient-to-br from-indigo-500 to-purple-600">
                  {selectedProject.image ? (
                    <img 
                      src={selectedProject.image} 
                      alt={selectedProject.name} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      {getCategoryIcon(selectedProject.category)}
                    </div>
                  )}
                  <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/80"></div>
                </div>
                
                <button 
                  className="absolute top-4 right-4 p-2 bg-black/20 hover:bg-black/40 rounded-full text-white transition-all"
                  onClick={() => setSelectedProject(null)}
                >
                  <X className="h-5 w-5" />
                </button>
                
                <div className="absolute bottom-0 left-0 w-full p-6 text-white">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="bg-indigo-600/80 backdrop-blur-sm text-white text-xs font-semibold px-2.5 py-1 rounded-full flex items-center gap-1">
                      {getCategoryIcon(selectedProject.category)}
                      {selectedProject.category}
                    </span>
                    {selectedProject.featured && (
                      <span className="bg-amber-500/80 backdrop-blur-sm text-white text-xs font-semibold px-2.5 py-1 rounded-full flex items-center gap-1">
                        <Star className="w-3 h-3 fill-current" /> Featured
                      </span>
                    )}
                  </div>
                  <h2 className="text-2xl sm:text-3xl font-bold">{selectedProject.name}</h2>
                  <div className="flex items-center mt-2">
                    <Clock className="w-4 h-4 mr-1.5" />
                    <span>{selectedProject.period}</span>
                  </div>
                </div>
              </div>
              
              <div className="p-6 sm:p-8">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Project Overview</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">{selectedProject.description}</p>
                
                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Technologies</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedProject.technologies.map((tech, index) => (
                      <motion.span
                        key={index}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.05 }}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300"
                      >
                        <Tag className="w-4 h-4 mr-1.5" />
                        {tech}
                      </motion.span>
                    ))}
                  </div>
                </div>
                
                {(selectedProject.github || selectedProject.demo) && (
                  <div className="flex flex-wrap gap-4 mt-8">
                    {selectedProject.github && (
                      <a
                        href={selectedProject.github}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        <Github className="w-5 h-5 mr-2" />
                        View Source
                      </a>
                    )}
                    {selectedProject.demo && (
                      <a
                        href={selectedProject.demo}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                      >
                        <ExternalLink className="w-5 h-5 mr-2" />
                        Live Demo
                      </a>
                    )}
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </MotionWrapper>
  );
}

// Star icon component
function Star(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
    </svg>
  );
} 