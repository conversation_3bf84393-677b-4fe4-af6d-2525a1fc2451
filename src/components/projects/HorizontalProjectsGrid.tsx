'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Globe, Calendar, Code, Laptop, ShoppingCart, Database, Server, ExternalLink, Github, Star } from 'lucide-react';
import { useScrollReveal, useMagneticEffect } from '../animations/GSAPAnimations';
import Image from 'next/image';

interface ProjectItem {
  id: number;
  name: string;
  period: string;
  description: string;
  category: string;
  technologies: string[];
  github?: string;
  demo?: string;
  image?: string;
  featured?: boolean;
}

const projects: ProjectItem[] = [
  {
    id: 1,
    name: 'Online Payment Gateway',
    period: 'January 2024 - April 2024',
    description: 'Led a team of 5 to implement and integrate an online payment gateway, enhancing the customer experience and streamlining the online payment process.',
    category: 'Web Development',
    technologies: ['API Integration', 'Payment Processing', 'Shopify'],

    featured: true
  },
  {
    id: 2,
    name: 'E-commerce Platform',
    period: 'March 2024 - June 2024',
    description: 'Built a comprehensive e-commerce platform with advanced features including inventory management and analytics.',
    category: 'E-commerce',
    technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
    featured: true
  },
  {
    id: 3,
    name: 'Mobile Delivery App',
    period: 'February 2024 - May 2024',
    description: 'Developed a cross-platform mobile application for delivery tracking and management.',
    category: 'Mobile Development',
    technologies: ['Flutter', 'Firebase', 'Google Maps API'],
  },
  {
    id: 4,
    name: 'Enterprise Dashboard',
    period: 'January 2024 - March 2024',
    description: 'Created a comprehensive dashboard for enterprise resource planning and analytics.',
    category: 'Enterprise Systems',
    technologies: ['Vue.js', 'Python', 'PostgreSQL', 'Docker'],
  },
  {
    id: 5,
    name: 'API Gateway Service',
    period: 'December 2023 - February 2024',
    description: 'Designed and implemented a scalable API gateway for microservices architecture.',
    category: 'Backend Development',
    technologies: ['Node.js', 'Express', 'Redis', 'AWS'],
  }
];

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'Web Development':
      return <Globe className="h-5 w-5" />;
    case 'Mobile Development':
      return <Laptop className="h-5 w-5" />;
    case 'E-commerce':
      return <ShoppingCart className="h-5 w-5" />;
    case 'Enterprise Systems':
      return <Database className="h-5 w-5" />;
    case 'Backend Development':
      return <Server className="h-5 w-5" />;
    default:
      return <Code className="h-5 w-5" />;
  }
};

export default function HorizontalProjectsGrid() {
  const [selectedProject, setSelectedProject] = useState<ProjectItem | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  // GSAP animations
  useScrollReveal('.project-card', {
    from: { opacity: 0, y: 100, rotationX: -15 },
    to: { opacity: 1, y: 0, rotationX: 0, duration: 1 }
  });
  
  useMagneticEffect('.magnetic-btn', 0.2);

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -400, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 400, behavior: 'smooth' });
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollButtons);
      checkScrollButtons();
      return () => container.removeEventListener('scroll', checkScrollButtons);
    }
  }, []);

  return (
    <div className="relative">
      {/* Section Header */}
      <div className="text-center mb-12">
        <h2 className="section-title">Featured Projects</h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Explore my latest work and creative solutions across various technologies and platforms.
        </p>
      </div>

      {/* Horizontal Scroll Container */}
      <div className="relative">
        {/* Left Scroll Button */}
        {canScrollLeft && (
          <button
            onClick={scrollLeft}
            className="scroll-indicator scroll-indicator-left magnetic-btn text-white"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
        )}

        {/* Right Scroll Button */}
        {canScrollRight && (
          <button
            onClick={scrollRight}
            className="scroll-indicator scroll-indicator-right magnetic-btn text-white"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        )}

        {/* Projects Container */}
        <div
          ref={scrollContainerRef}
          className="horizontal-scroll py-8"
          style={{ paddingLeft: '2rem', paddingRight: '2rem' }}
        >
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              className="horizontal-scroll-item project-card"
              style={{ width: '350px' }}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div 
                className="hover-card h-[450px] cursor-pointer group"
                onClick={() => setSelectedProject(project)}
              >
                {project.featured && (
                  <div className="absolute top-4 left-4 z-10">
                    <span className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-semibold px-3 py-1 rounded-full flex items-center gap-1">
                      <Star className="w-3 h-3 fill-current" /> Featured
                    </span>
                  </div>
                )}
                
                <div className="h-48 relative overflow-hidden">
                  {project.image ? (
                    <img 
                      src={project.image} 
                      alt={project.name}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-primary to-accent flex items-center justify-center text-white">
                      {getCategoryIcon(project.category)}
                    </div>
                  )}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
                    <div className="absolute bottom-4 left-4">
                      <div className="flex items-center gap-2 text-white">
                        {getCategoryIcon(project.category)}
                        <span className="text-sm font-medium">{project.category}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-6 flex flex-col h-[calc(100%-12rem)]">
                  <h3 className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors">
                    {project.name}
                  </h3>
                  
                  <div className="flex items-center text-sm text-muted-foreground mb-3">
                    <Calendar className="h-4 w-4 mr-2" />
                    {project.period}
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-4 flex-grow line-clamp-3">
                    {project.description}
                  </p>
                  
                  <div className="flex flex-wrap gap-2 mt-auto">
                    {project.technologies.slice(0, 3).map((tech, i) => (
                      <span
                        key={i}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary border border-primary/20"
                      >
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 3 && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-muted text-muted-foreground">
                        +{project.technologies.length - 3} more
                      </span>
                    )}
                  </div>
                  
                  <div className="flex gap-2 mt-4">
                    {project.github && (
                      <button className="magnetic-btn flex items-center gap-1 px-3 py-1 text-xs bg-secondary text-secondary-foreground rounded-full hover:bg-secondary/80 transition-colors">
                        <Github className="h-3 w-3" />
                        Code
                      </button>
                    )}
                    {project.demo && (
                      <button className="magnetic-btn flex items-center gap-1 px-3 py-1 text-xs bg-primary text-primary-foreground rounded-full hover:bg-primary/90 transition-colors">
                        <ExternalLink className="h-3 w-3" />
                        Demo
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Project Detail Modal */}
      <AnimatePresence>
        {selectedProject && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedProject(null)}
          >
            <motion.div
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
              className="bg-card rounded-2xl shadow-2xl overflow-hidden max-w-2xl w-full max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="h-64 relative">
                {selectedProject.image ? (
                  <img 
                    src={selectedProject.image} 
                    alt={selectedProject.name} 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-primary to-accent flex items-center justify-center text-white">
                    {getCategoryIcon(selectedProject.category)}
                  </div>
                )}
                <button 
                  className="absolute top-4 right-4 p-2 bg-black/20 hover:bg-black/40 rounded-full text-white transition-all"
                  onClick={() => setSelectedProject(null)}
                >
                  ×
                </button>
              </div>
              
              <div className="p-6">
                <h3 className="text-2xl font-bold text-foreground mb-2">{selectedProject.name}</h3>
                <div className="flex items-center text-muted-foreground mb-4">
                  <Calendar className="h-4 w-4 mr-2" />
                  {selectedProject.period}
                </div>
                <p className="text-muted-foreground mb-6">{selectedProject.description}</p>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-foreground mb-3">Technologies Used</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedProject.technologies.map((tech, i) => (
                      <span
                        key={i}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary border border-primary/20"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="flex gap-3">
                  {selectedProject.github && (
                    <button className="btn-secondary flex items-center gap-2">
                      <Github className="h-4 w-4" />
                      View Code
                    </button>
                  )}
                  {selectedProject.demo && (
                    <button className="btn-primary flex items-center gap-2">
                      <ExternalLink className="h-4 w-4" />
                      Live Demo
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
