'use client'

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import ResponsiveGrid from './ResponsiveGrid';
import Image from 'next/image';
import {
  Terminal,
  Code,
  Database,
  Zap,
  TrendingUp,
  Users,
  Award,
  Cpu,
  BarChart3,
  Settings,
  ExternalLink,
  Github,
  Globe,
  Calendar,
  MapPin,
  Truck
} from 'lucide-react';

// Cyber project data structure
interface CyberProject {
  id: string;
  title: string;
  type: string;
  status: string;
  description: string;
  technologies: string[];
  impact: string;
  metrics: string;
  icon: React.ReactNode;
  color: string;
  image?: string;
}

// Cyber projects data
const cyberProjects: CyberProject[] = [
  {
    id: 'zawaya-erp-system',
    title: 'ERP_SYSTEM.ZAWAYA',
    type: 'ENTERPRISE_SYSTEM',
    status: 'DEVELOPING',
    description: 'Currently developing a full-scale ERP system that integrates all existing business applications into a unified platform, designed to simplify user workflows and centralize business operations.',
    technologies: ['ERP Architecture', 'System Integration', 'Database Design', 'APIs'],
    impact: 'Unified platform for all business operations',
    metrics: 'Jun 2025 – present',
    icon: <Settings className="w-6 h-6" />,
    color: 'primary'
  },
  {
    id: 'web-delivery-system',
    title: 'DELIVERY_MANAGEMENT.WEB',
    type: 'WEB_APPLICATION',
    status: 'DEPLOYED',
    description: 'Built a comprehensive delivery management system that optimized route planning and delivery scheduling, significantly reducing wasted time and operational costs.',
    technologies: ['Route Optimization', 'Scheduling Algorithms', 'Web Development', 'APIs'],
    impact: 'Optimized delivery operations and cost reduction',
    metrics: 'May 2025 – present',
    icon: <Truck className="w-6 h-6" />,
    color: 'accent',
    image: '/assets/images/Web-Delivery.png'
  },
  {
    id: 'inventory-system',
    title: 'INVENTORY_MANAGEMENT.SYS',
    type: 'MANAGEMENT_SYSTEM',
    status: 'DEPLOYED',
    description: 'Developed an advanced inventory management system with automated stock tracking, demand forecasting capabilities, and streamlined product in/out processes.',
    technologies: ['Automation', 'Demand Forecasting', 'Stock Tracking', 'Process Optimization'],
    impact: 'Organized and efficient warehouse operation',
    metrics: 'Apr 2025 – Jun 2025',
    icon: <Database className="w-6 h-6" />,
    color: 'secondary',
    image: '/assets/images/inventory.png'
  },
  {
    id: 'crm-project',
    title: 'CRM_PLATFORM.ZAWAYA',
    type: 'CRM_SYSTEM',
    status: 'EXPANDED',
    description: 'Architected and expanded the CRM system to serve as a comprehensive documentation platform for all business processes, order management, and client relationship tracking.',
    technologies: ['CRM Architecture', 'Documentation Systems', 'Order Management', 'Client Tracking'],
    impact: 'Comprehensive business process documentation',
    metrics: 'Feb 2025 – present',
    icon: <Users className="w-6 h-6" />,
    color: 'warning',
    image: '/assets/images/CRM.png'
  },
  {
    id: 'shopify-website',
    title: 'ECOMMERCE_PLATFORM.ZAWAYA',
    type: 'WEB_APPLICATION',
    status: 'ACTIVE',
    description: 'Developed a comprehensive Zawaya e-commerce platform using Shopify, implementing custom features and optimizations that increased customer orders by 25% and improved overall user experience.',
    technologies: ['Shopify', 'E-commerce', 'Payment Integration', 'UX Design', 'Custom Development'],
    impact: 'Enhanced customer experience and increased sales by 25%',
    metrics: 'Jan 2025 – present',
    icon: <Globe className="w-6 h-6" />,
    color: 'primary',
    image: '/assets/images/Zawaya.png'
  },
  {
    id: 'flutter-delivery-app',
    title: 'DELIVERY_TRACKER.FLUTTER',
    type: 'MOBILE_APPLICATION',
    status: 'DEPLOYED',
    description: 'Designed, coded, and tested a Flutter-based delivery app that provides real-time tracking of agents performance and availability, enhancing operational efficiency.',
    technologies: ['Flutter', 'Real-time Tracking', 'Performance Analytics', 'Mobile Development'],
    impact: 'Enhanced operational efficiency and agent management',
    metrics: 'Nov 2024 – May 2025',
    icon: <Terminal className="w-6 h-6" />,
    color: 'accent'
  },
  {
    id: 'online-payment-gateway',
    title: 'PAYMENT_GATEWAY.INTEGRATION',
    type: 'PAYMENT_SYSTEM',
    status: 'DEPLOYED',
    description: 'Led a team of 5 to implement and integrate an online payment gateway, enhancing the customer experience and streamlining the online payment process.',
    technologies: ['Payment APIs', 'Team Leadership', 'Integration', 'Customer Experience'],
    impact: 'Streamlined payment process and enhanced UX',
    metrics: 'Jan 2024 – Apr 2024',
    icon: <Award className="w-6 h-6" />,
    color: 'warning'
  },
  {
    id: 'zawaya-backend',
    title: 'BACKEND_SYSTEM.ZAWAYA',
    type: 'BACKEND_SYSTEM',
    status: 'RUNNING',
    description: 'Developed the back-end system for Zawaya from scratch to comply with specific business needs, enhancing cost reduction and system reliability.',
    technologies: ['Backend Development', 'System Architecture', 'Cost Optimization', 'Reliability'],
    impact: 'Enhanced cost reduction and system reliability',
    metrics: 'Jan 2023 – present',
    icon: <Cpu className="w-6 h-6" />,
    color: 'secondary'
  },
  {
    id: 'compliance-dashboard',
    title: 'COMPLIANCE_DASHBOARD.SWVL',
    type: 'MONITORING_SYSTEM',
    status: 'DEPLOYED',
    description: 'Implemented an automated report to proactively flag compliance violations arising from customer, captain, or employee behavior.',
    technologies: ['Automation', 'Compliance Monitoring', 'Reporting', 'Data Analysis'],
    impact: 'Proactive compliance violation detection',
    metrics: '2020',
    icon: <BarChart3 className="w-6 h-6" />,
    color: 'warning'
  },
  {
    id: 'delivery-management-misr',
    title: 'DELIVERY_SYSTEM.MISR_PHARMACIES',
    type: 'MANAGEMENT_SYSTEM',
    status: 'DEPLOYED',
    description: 'Led system implementation and staff training across 40+ company branches, ensuring operational readiness and enhanced customer experience.',
    technologies: ['System Implementation', 'Staff Training', 'Operations Management', 'Customer Experience'],
    impact: 'Operational readiness across 40+ branches',
    metrics: '2021',
    icon: <Users className="w-6 h-6" />,
    color: 'primary'
  },
  {
    id: 'live-ops-capiter',
    title: 'LIVE_OPS.CAPITER',
    type: 'OPERATIONS_SYSTEM',
    status: 'DEPLOYED',
    description: 'Established a team to identify daily violations in real-time and spearheaded a project that exposed extensive fraudulent orders, saving the company millions of EGP.',
    technologies: ['Real-time Monitoring', 'Fraud Detection', 'Team Management', 'Operations'],
    impact: 'Saved millions in fraudulent order prevention',
    metrics: '2021',
    icon: <Award className="w-6 h-6" />,
    color: 'accent'
  }
];

const Portfolio: React.FC = () => {

  return (
    <div className="space-responsive">
      {/* Cyber Projects Grid */}
      <ResponsiveGrid
        cols={{ xs: 1, sm: 2, lg: 3 }}
        gap="md"
        animate={true}
        stagger={0.1}
      >
        {cyberProjects.map((project, index) => (
          <motion.div
            key={project.id}
            whileHover={{ y: -8, scale: 1.02 }}
            className="cyber-card group h-full flex flex-col"
          >
            {/* Project Status Bar */}
            <div className="flex items-center justify-between p-3 sm:p-4 border-b border-border">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                <span className="font-mono text-xs text-primary">{project.status}</span>
              </div>
              <div className="font-mono text-xs text-muted-foreground hidden sm:block">{project.type}</div>
            </div>

            <div className="p-4 sm:p-6 flex flex-col flex-1">
              {/* Project Image */}
              {project.image && (
                <div className="mb-4 rounded-lg overflow-hidden">
                  <Image
                    src={project.image}
                    alt={project.title}
                    width={400}
                    height={200}
                    className="w-full h-32 sm:h-40 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
              )}

              {/* Project Icon and Title */}
              <div className="flex items-start mb-4">
                <div className="p-2 sm:p-3 rounded-lg bg-primary/10 text-primary mr-3 sm:mr-4 group-hover:bg-primary/20 transition-colors flex-shrink-0">
                  {project.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-base sm:text-lg font-bold font-mono text-foreground mb-1 break-words">{project.title}</h3>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-accent rounded-full"></div>
                    <span className="text-xs font-mono text-accent uppercase truncate">{project.color}_MODULE</span>
                  </div>
                </div>
              </div>

              {/* Project Description */}
              <p className="text-muted-foreground text-sm leading-relaxed mb-4 flex-1">
                {project.description}
              </p>

              {/* Technologies */}
              <div className="mb-4">
                <div className="text-xs font-mono text-muted-foreground mb-2">TECH_STACK:</div>
                <div className="flex flex-wrap gap-1">
                  {project.technologies.map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="px-2 py-1 bg-muted rounded text-xs font-mono text-muted-foreground"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Impact Metrics */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-xs font-mono">
                  <span className="text-muted-foreground">IMPACT</span>
                  <span className="text-primary">{project.metrics}</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <motion.div
                    className="h-2 bg-gradient-to-r from-primary to-accent rounded-full"
                    initial={{ width: 0 }}
                    whileInView={{ width: '90%' }}
                    transition={{ duration: 1.5, delay: index * 0.2 }}
                    viewport={{ once: true }}
                  />
                </div>
              </div>

              {/* Project Footer */}
              <div className="flex items-center justify-between pt-4 border-t border-border">
                <div className="flex items-center gap-2 text-xs font-mono text-muted-foreground">
                  <Code className="w-3 h-3" />
                  <span>DEPLOYED</span>
                </div>
                <motion.div
                  className="text-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  whileHover={{ x: 5 }}
                >
                  <ExternalLink className="h-4 w-4" />
                </motion.div>
              </div>
            </div>
          </motion.div>
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default Portfolio;